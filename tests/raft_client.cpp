/**
 * @file raft_client.cpp
 * @brief Raft-KV客户端全面功能测试
 *
 * 该文件实现了一个全面的Raft-KV客户端测试程序，用于验证：
 * - 基本的Put/Get/Append操作
 * - 并发客户端操作
 * - 故障恢复测试
 * - 性能基准测试
 * - 一致性验证
 *
 * 配合raft_test使用，通过cmake编译
 *
 * <AUTHOR> Team
 * @date 2024
 */

#include <iostream>
#include <string>
#include <vector>
#include <thread>
#include <chrono>
#include <atomic>
#include <random>
#include <signal.h>
#include <unistd.h>
#include <getopt.h>
#include "raft-kv/raftClerk/clerk.h"
#include "raft-kv/common/util.h"

// 全局统计计数器
std::atomic<int> g_total_operations{0};
std::atomic<int> g_successful_operations{0};
std::atomic<int> g_failed_operations{0};
std::atomic<bool> g_test_running{true};

// ==================== 1. 工具函数 ====================

/**
 * @brief 信号处理函数
 */
void signal_handler(int sig)
{
    std::cout << "\n[客户端] 收到信号 " << sig << "，正在退出..." << std::endl;
    g_test_running = false;
}

/**
 * @brief 等待指定时间
 */
void wait_seconds(int seconds, const std::string &message = "")
{
    if (!message.empty())
    {
        std::cout << "[客户端] " << message << "，等待 " << seconds << " 秒..." << std::endl;
    }
    std::this_thread::sleep_for(std::chrono::seconds(seconds));
}

/**
 * @brief 生成随机字符串
 */
std::string generate_random_string(int length)
{
    static const char charset[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, sizeof(charset) - 2);

    std::string result;
    result.reserve(length);
    for (int i = 0; i < length; i++)
    {
        result += charset[dis(gen)];
    }
    return result;
}

// ==================== 2. 基本功能测试 ====================

/**
 * @brief 基本Put/Get/Append操作测试
 */
bool test_basic_operations(Clerk &client)
{
    std::cout << "\n=== 基本操作测试 ===" << std::endl;

    bool all_success = true;

    // 测试1: Put操作
    std::cout << "\n[测试1] Put操作测试" << std::endl;
    std::string key = "用户_张三";
    std::string value = "软件工程师";
    std::cout << "[客户端] 存储键值对: '" << key << "' = '" << value << "'" << std::endl;

    try
    {
        client.Put(key, value);
        g_total_operations++;
        g_successful_operations++;
        std::cout << "[客户端] Put操作成功" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "[客户端] Put操作失败: " << e.what() << std::endl;
        g_failed_operations++;
        all_success = false;
    }

    wait_seconds(1, "等待Raft日志同步");

    // 测试2: Get操作
    std::cout << "\n[测试2] Get操作测试" << std::endl;
    std::cout << "[客户端] 获取键: '" << key << "'" << std::endl;

    try
    {
        std::string retrieved_value = client.Get(key);
        g_total_operations++;

        std::cout << "[客户端] 获取到值: '" << retrieved_value << "'" << std::endl;

        if (retrieved_value == value)
        {
            std::cout << "[成功] 获取的值与存储的值一致！" << std::endl;
            g_successful_operations++;
        }
        else
        {
            std::cerr << "[失败] 获取的值与存储的值不一致！" << std::endl;
            std::cerr << "  期望: '" << value << "'" << std::endl;
            std::cerr << "  实际: '" << retrieved_value << "'" << std::endl;
            g_failed_operations++;
            all_success = false;
        }
    }
    catch (const std::exception &e)
    {
        std::cerr << "[客户端] Get操作失败: " << e.what() << std::endl;
        g_failed_operations++;
        all_success = false;
    }

    // 测试3: Append操作
    std::cout << "\n[测试3] Append操作测试" << std::endl;
    std::string append_value = "_资深";
    std::cout << "[客户端] 追加值: '" << append_value << "' 到键 '" << key << "'" << std::endl;

    try
    {
        client.Append(key, append_value);
        g_total_operations++;
        g_successful_operations++;
        std::cout << "[客户端] Append操作成功" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "[客户端] Append操作失败: " << e.what() << std::endl;
        g_failed_operations++;
        all_success = false;
    }

    wait_seconds(1, "等待Raft日志同步");

    // 测试4: 验证Append结果
    std::cout << "\n[测试4] 验证Append结果" << std::endl;
    std::cout << "[客户端] 再次获取键: '" << key << "'" << std::endl;

    try
    {
        std::string final_value = client.Get(key);
        g_total_operations++;

        std::cout << "[客户端] 最终值: '" << final_value << "'" << std::endl;

        std::string expected_value = value + append_value;
        if (final_value == expected_value)
        {
            std::cout << "[成功] Append操作结果正确！" << std::endl;
            g_successful_operations++;
        }
        else
        {
            std::cerr << "[失败] Append操作结果不正确！" << std::endl;
            std::cerr << "  期望: '" << expected_value << "'" << std::endl;
            std::cerr << "  实际: '" << final_value << "'" << std::endl;
            g_failed_operations++;
            all_success = false;
        }
    }
    catch (const std::exception &e)
    {
        std::cerr << "[客户端] 验证Get操作失败: " << e.what() << std::endl;
        g_failed_operations++;
        all_success = false;
    }

    std::cout << "\n=== 基本操作测试完成 ===" << std::endl;
    return all_success;
}

// ==================== 3. 并发测试 ====================

/**
 * @brief 并发客户端操作测试
 */
void test_concurrent_operations(const std::string &config_file, int thread_count, int operations_per_thread)
{
    std::cout << "\n=== 并发操作测试 ===" << std::endl;
    std::cout << "[并发测试] 线程数: " << thread_count << ", 每线程操作数: " << operations_per_thread << std::endl;

    std::vector<std::thread> threads;
    std::atomic<int> concurrent_success{0};
    std::atomic<int> concurrent_failed{0};

    auto start_time = std::chrono::high_resolution_clock::now();

    // 创建多个线程进行并发操作
    for (int i = 0; i < thread_count; i++)
    {
        threads.emplace_back([&, i, config_file, operations_per_thread]()
                             {
            try {
                Clerk client;
                client.Init(config_file.c_str());

                std::cout << "[线程" << i << "] 客户端初始化完成" << std::endl;

                for (int j = 0; j < operations_per_thread && g_test_running; j++) {
                    try {
                        std::string key = "并发测试_线程" + std::to_string(i) + "_操作" + std::to_string(j);
                        std::string value = "值_" + generate_random_string(10);

                        // Put操作
                        client.Put(key, value);

                        // 短暂延迟
                        std::this_thread::sleep_for(std::chrono::milliseconds(10));

                        // Get操作验证
                        std::string retrieved = client.Get(key);
                        if (retrieved == value) {
                            concurrent_success++;
                            if (j % 5 == 0) {  // 每5个操作输出一次
                                std::cout << "[线程" << i << "] 操作 " << j << " 成功" << std::endl;
                            }
                        } else {
                            concurrent_failed++;
                            std::cerr << "[线程" << i << "] 操作 " << j << " 验证失败" << std::endl;
                        }

                    } catch (const std::exception& e) {
                        concurrent_failed++;
                        std::cerr << "[线程" << i << "] 操作异常: " << e.what() << std::endl;
                    }
                }

                std::cout << "[线程" << i << "] 完成所有操作" << std::endl;

            } catch (const std::exception& e) {
                std::cerr << "[线程" << i << "] 初始化失败: " << e.what() << std::endl;
            } });
    }

    // 等待所有线程完成
    for (auto &thread : threads)
    {
        thread.join();
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    std::cout << "\n[并发测试] 测试结果:" << std::endl;
    std::cout << "  - 总操作数: " << (thread_count * operations_per_thread * 2) << " (Put+Get)" << std::endl;
    std::cout << "  - 成功操作数: " << concurrent_success.load() << std::endl;
    std::cout << "  - 失败操作数: " << concurrent_failed.load() << std::endl;
    std::cout << "  - 总耗时: " << duration.count() << "ms" << std::endl;
    if (concurrent_success.load() + concurrent_failed.load() > 0)
    {
        double success_rate = 100.0 * concurrent_success.load() / (concurrent_success.load() + concurrent_failed.load());
        std::cout << "  - 成功率: " << success_rate << "%" << std::endl;
        std::cout << "  - 吞吐量: " << (concurrent_success.load() * 1000.0 / duration.count()) << " 操作/秒" << std::endl;
    }

    g_total_operations += concurrent_success.load() + concurrent_failed.load();
    g_successful_operations += concurrent_success.load();
    g_failed_operations += concurrent_failed.load();

    std::cout << "=== 并发操作测试完成 ===" << std::endl;
}

// ==================== 4. 性能基准测试 ====================

/**
 * @brief 性能基准测试
 */
void test_performance_benchmark(Clerk &client, int operation_count)
{
    std::cout << "\n=== 性能基准测试 ===" << std::endl;
    std::cout << "[性能测试] 操作数量: " << operation_count << std::endl;

    // 预热阶段
    std::cout << "[性能测试] 预热阶段..." << std::endl;
    for (int i = 0; i < 10; i++)
    {
        std::string key = "预热_" + std::to_string(i);
        std::string value = "预热值";
        try
        {
            client.Put(key, value);
            client.Get(key);
        }
        catch (...)
        {
            // 忽略预热阶段的错误
        }
    }

    // 基准测试阶段
    std::cout << "[性能测试] 开始基准测试..." << std::endl;

    std::vector<double> put_latencies;
    std::vector<double> get_latencies;
    std::vector<double> append_latencies;

    put_latencies.reserve(operation_count);
    get_latencies.reserve(operation_count);
    append_latencies.reserve(operation_count / 2);

    auto total_start = std::chrono::high_resolution_clock::now();

    for (int i = 0; i < operation_count && g_test_running; i++)
    {
        std::string key = "性能测试_" + std::to_string(i);
        std::string value = "测试值_" + generate_random_string(20);

        try
        {
            // Put操作测试
            auto put_start = std::chrono::high_resolution_clock::now();
            client.Put(key, value);
            auto put_end = std::chrono::high_resolution_clock::now();

            double put_latency = std::chrono::duration<double, std::milli>(put_end - put_start).count();
            put_latencies.push_back(put_latency);

            // Get操作测试
            auto get_start = std::chrono::high_resolution_clock::now();
            std::string retrieved = client.Get(key);
            auto get_end = std::chrono::high_resolution_clock::now();

            double get_latency = std::chrono::duration<double, std::milli>(get_end - get_start).count();
            get_latencies.push_back(get_latency);

            // 验证正确性
            if (retrieved == value)
            {
                g_successful_operations += 2; // Put + Get
            }
            else
            {
                g_failed_operations += 2;
                std::cerr << "[性能测试] 操作 " << i << " 验证失败" << std::endl;
            }

            // 每隔一个操作进行Append测试
            if (i % 2 == 0)
            {
                std::string append_val = "_追加";
                auto append_start = std::chrono::high_resolution_clock::now();
                client.Append(key, append_val);
                auto append_end = std::chrono::high_resolution_clock::now();

                double append_latency = std::chrono::duration<double, std::milli>(append_end - append_start).count();
                append_latencies.push_back(append_latency);
                g_successful_operations++;
            }

            g_total_operations += (i % 2 == 0) ? 3 : 2; // Put + Get + (可能的Append)

            if (i % 100 == 0 && i > 0)
            {
                std::cout << "[性能测试] 已完成 " << i << " 个操作" << std::endl;
            }
        }
        catch (const std::exception &e)
        {
            std::cerr << "[性能测试] 操作 " << i << " 失败: " << e.what() << std::endl;
            g_failed_operations += 2;
            g_total_operations += 2;
        }
    }

    auto total_end = std::chrono::high_resolution_clock::now();
    auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(total_end - total_start);

    // 计算统计信息
    auto calculate_stats = [](const std::vector<double> &latencies)
    {
        if (latencies.empty())
            return std::make_tuple(0.0, 0.0, 0.0, 0.0);

        std::vector<double> sorted = latencies;
        std::sort(sorted.begin(), sorted.end());

        double sum = std::accumulate(sorted.begin(), sorted.end(), 0.0);
        double avg = sum / sorted.size();
        double p50 = sorted[sorted.size() / 2];
        double p99 = sorted[sorted.size() * 99 / 100];

        return std::make_tuple(avg, p50, p99, sorted.back());
    };

    auto [put_avg, put_p50, put_p99, put_max] = calculate_stats(put_latencies);
    auto [get_avg, get_p50, get_p99, get_max] = calculate_stats(get_latencies);
    auto [append_avg, append_p50, append_p99, append_max] = calculate_stats(append_latencies);

    std::cout << "\n[性能测试] 测试结果:" << std::endl;
    std::cout << "  - 总耗时: " << total_duration.count() << "ms" << std::endl;
    std::cout << "  - 总QPS: " << (operation_count * 1000.0 / total_duration.count()) << std::endl;
    std::cout << "\n  Put操作延迟统计 (ms):" << std::endl;
    std::cout << "    平均: " << put_avg << ", P50: " << put_p50 << ", P99: " << put_p99 << ", 最大: " << put_max << std::endl;
    std::cout << "  Get操作延迟统计 (ms):" << std::endl;
    std::cout << "    平均: " << get_avg << ", P50: " << get_p50 << ", P99: " << get_p99 << ", 最大: " << get_max << std::endl;
    std::cout << "  Append操作延迟统计 (ms):" << std::endl;
    std::cout << "    平均: " << append_avg << ", P50: " << append_p50 << ", P99: " << append_p99 << ", 最大: " << append_max << std::endl;

    std::cout << "=== 性能基准测试完成 ===" << std::endl;
}

// ==================== 5. 一致性验证测试 ====================

/**
 * @brief 一致性验证测试
 */
void test_consistency_verification(Clerk &client)
{
    std::cout << "\n=== 一致性验证测试 ===" << std::endl;

    const int TEST_KEYS = 20;
    std::vector<std::pair<std::string, std::string>> test_data;

    // 准备测试数据
    std::cout << "[一致性测试] 准备测试数据..." << std::endl;
    for (int i = 0; i < TEST_KEYS; i++)
    {
        std::string key = "一致性测试_" + std::to_string(i);
        std::string value = "初始值_" + generate_random_string(15);
        test_data.emplace_back(key, value);

        try
        {
            client.Put(key, value);
            g_total_operations++;
            g_successful_operations++;
        }
        catch (const std::exception &e)
        {
            std::cerr << "[一致性测试] 准备数据失败: " << e.what() << std::endl;
            g_failed_operations++;
        }
    }

    wait_seconds(2, "等待数据同步");

    // 验证数据一致性
    std::cout << "[一致性测试] 验证数据一致性..." << std::endl;
    int consistent_count = 0;

    for (const auto &[key, expected_value] : test_data)
    {
        try
        {
            std::string retrieved = client.Get(key);
            g_total_operations++;

            if (retrieved == expected_value)
            {
                consistent_count++;
                g_successful_operations++;
            }
            else
            {
                std::cerr << "[一致性测试] 键 '" << key << "' 不一致" << std::endl;
                std::cerr << "  期望: '" << expected_value << "'" << std::endl;
                std::cerr << "  实际: '" << retrieved << "'" << std::endl;
                g_failed_operations++;
            }
        }
        catch (const std::exception &e)
        {
            std::cerr << "[一致性测试] 获取键 '" << key << "' 失败: " << e.what() << std::endl;
            g_failed_operations++;
        }
    }

    // 进行一些修改操作
    std::cout << "[一致性测试] 执行修改操作..." << std::endl;
    for (int i = 0; i < TEST_KEYS / 2; i++)
    {
        std::string key = test_data[i].first;
        std::string append_val = "_修改" + std::to_string(i);

        try
        {
            client.Append(key, append_val);
            test_data[i].second += append_val; // 更新期望值
            g_total_operations++;
            g_successful_operations++;
        }
        catch (const std::exception &e)
        {
            std::cerr << "[一致性测试] 修改键 '" << key << "' 失败: " << e.what() << std::endl;
            g_failed_operations++;
        }
    }

    wait_seconds(2, "等待修改同步");

    // 再次验证一致性
    std::cout << "[一致性测试] 再次验证一致性..." << std::endl;
    int final_consistent_count = 0;

    for (const auto &[key, expected_value] : test_data)
    {
        try
        {
            std::string retrieved = client.Get(key);
            g_total_operations++;

            if (retrieved == expected_value)
            {
                final_consistent_count++;
                g_successful_operations++;
            }
            else
            {
                std::cerr << "[一致性测试] 修改后键 '" << key << "' 不一致" << std::endl;
                g_failed_operations++;
            }
        }
        catch (const std::exception &e)
        {
            std::cerr << "[一致性测试] 获取修改后键 '" << key << "' 失败: " << e.what() << std::endl;
            g_failed_operations++;
        }
    }

    std::cout << "\n[一致性测试] 测试结果:" << std::endl;
    std::cout << "  - 初始一致性: " << consistent_count << "/" << TEST_KEYS << " ("
              << (100.0 * consistent_count / TEST_KEYS) << "%)" << std::endl;
    std::cout << "  - 修改后一致性: " << final_consistent_count << "/" << TEST_KEYS << " ("
              << (100.0 * final_consistent_count / TEST_KEYS) << "%)" << std::endl;

    std::cout << "=== 一致性验证测试完成 ===" << std::endl;
}

// ==================== 6. 主函数 ====================

/**
 * @brief 显示使用说明
 */
void show_usage(const char *program_name)
{
    std::cout << "用法: " << program_name << " [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  -c <配置文件>  指定配置文件路径 (默认: test.conf)" << std::endl;
    std::cout << "  -t <测试类型>  指定测试类型 (默认: all)" << std::endl;
    std::cout << "                 basic      - 基本操作测试" << std::endl;
    std::cout << "                 concurrent - 并发操作测试" << std::endl;
    std::cout << "                 performance - 性能基准测试" << std::endl;
    std::cout << "                 consistency - 一致性验证测试" << std::endl;
    std::cout << "                 all        - 所有测试" << std::endl;
    std::cout << "  -n <数量>      操作数量 (默认: 100)" << std::endl;
    std::cout << "  -j <线程数>    并发线程数 (默认: 3)" << std::endl;
    std::cout << "  -h             显示此帮助信息" << std::endl;
    std::cout << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  " << program_name << " -c test.conf -t basic" << std::endl;
    std::cout << "  " << program_name << " -t performance -n 500" << std::endl;
    std::cout << "  " << program_name << " -t concurrent -j 5 -n 50" << std::endl;
}

/**
 * @brief 主函数
 */
int main(int argc, char **argv)
{
    std::cout << "========================================" << std::endl;
    std::cout << "    Raft-KV客户端全面功能测试" << std::endl;
    std::cout << "========================================" << std::endl;

    // 设置信号处理
    signal(SIGPIPE, SIG_IGN);
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 默认参数
    std::string config_file = "test.conf";
    std::string test_type = "all";
    int operation_count = 100;
    int thread_count = 3;

    // 解析命令行参数
    int opt;
    while ((opt = getopt(argc, argv, "c:t:n:j:h")) != -1)
    {
        switch (opt)
        {
        case 'c':
            config_file = optarg;
            break;
        case 't':
            test_type = optarg;
            break;
        case 'n':
            operation_count = std::atoi(optarg);
            break;
        case 'j':
            thread_count = std::atoi(optarg);
            break;
        case 'h':
            show_usage(argv[0]);
            return 0;
        default:
            show_usage(argv[0]);
            return 1;
        }
    }

    // 验证参数
    if (operation_count <= 0 || thread_count <= 0)
    {
        std::cerr << "错误: 操作数量和线程数必须大于0" << std::endl;
        return 1;
    }

    std::cout << "[客户端配置] 配置文件: " << config_file << std::endl;
    std::cout << "[客户端配置] 测试类型: " << test_type << std::endl;
    std::cout << "[客户端配置] 操作数量: " << operation_count << std::endl;
    std::cout << "[客户端配置] 并发线程数: " << thread_count << std::endl;

    try
    {
        // 初始化客户端
        Clerk client;
        std::cout << "\n[客户端] 正在初始化，配置文件: " << config_file << std::endl;
        client.Init(config_file.c_str());
        std::cout << "[客户端] 初始化成功！" << std::endl;

        auto test_start_time = std::chrono::high_resolution_clock::now();

        // 执行测试
        if (test_type == "all" || test_type == "basic")
        {
            test_basic_operations(client);
        }

        if (test_type == "all" || test_type == "concurrent")
        {
            test_concurrent_operations(config_file, thread_count, operation_count / thread_count);
        }

        if (test_type == "all" || test_type == "performance")
        {
            test_performance_benchmark(client, operation_count);
        }

        if (test_type == "all" || test_type == "consistency")
        {
            test_consistency_verification(client);
        }

        auto test_end_time = std::chrono::high_resolution_clock::now();
        auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(test_end_time - test_start_time);

        // 显示最终统计
        std::cout << "\n========================================" << std::endl;
        std::cout << "    测试完成！" << std::endl;
        std::cout << "    最终统计:" << std::endl;
        std::cout << "    - 总操作数: " << g_total_operations.load() << std::endl;
        std::cout << "    - 成功操作数: " << g_successful_operations.load() << std::endl;
        std::cout << "    - 失败操作数: " << g_failed_operations.load() << std::endl;
        std::cout << "    - 总耗时: " << total_duration.count() << "ms" << std::endl;
        if (g_total_operations.load() > 0)
        {
            double success_rate = 100.0 * g_successful_operations.load() / g_total_operations.load();
            std::cout << "    - 成功率: " << success_rate << "%" << std::endl;
            std::cout << "    - 平均QPS: " << (g_total_operations.load() * 1000.0 / total_duration.count()) << std::endl;
        }
        std::cout << "========================================" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}